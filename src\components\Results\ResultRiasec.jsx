import { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import apiService from '../../services/apiService';
import EnhancedLoadingScreen from '../UI/EnhancedLoadingScreen';
import RiasecRadarChart from './RiasecRadarChart';
import AssessmentExplanations from './AssessmentExplanations';

const ResultRiasec = () => {
  const { resultId } = useParams();
  const navigate = useNavigate();
  const [result, setResult] = useState(null);
  const [error, setError] = useState('');
  const fetchInProgressRef = useRef(false);
  const abortControllerRef = useRef(null);

  useEffect(() => {
    // Prevent duplicate calls
    if (fetchInProgressRef.current) {
      return;
    }

    const fetchResult = async (retryCount = 0) => {
      const maxRetries = 5;
      const retryDelay = Math.min(1000 * Math.pow(2, retryCount), 10000); // Exponential backoff, max 10s

      // Create new AbortController for this fetch sequence
      abortControllerRef.current = new AbortController();

      try {
        fetchInProgressRef.current = true;
        const response = await apiService.getResultById(resultId);

        // Check if component is still mounted and request wasn't aborted
        if (!abortControllerRef.current?.signal.aborted) {
          if (response.success) {
            setResult(response.data);
            fetchInProgressRef.current = false;
          }
        }
      } catch (err) {
        // Check if the error is due to abort
        if (abortControllerRef.current?.signal.aborted) {
          return;
        }

        // If it's a 404 and we haven't exceeded max retries, try again
        if (err.response?.status === 404 && retryCount < maxRetries) {
          setTimeout(() => {
            // Check if component is still mounted before retrying
            if (!abortControllerRef.current?.signal.aborted) {
              fetchResult(retryCount + 1);
            }
          }, retryDelay);
        } else {
          // Final error after all retries or non-404 error
          const errorMessage = retryCount >= maxRetries
            ? `Result not found after ${maxRetries + 1} attempts. The analysis may still be processing.`
            : err.response?.data?.message || 'Failed to load results';
          setError(errorMessage);
          fetchInProgressRef.current = false;
        }
      }
    };

    if (resultId) {
      fetchResult();
    } else {
      navigate('/dashboard');
    }

    // Cleanup function
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      fetchInProgressRef.current = false;
    };
  }, [resultId, navigate]);

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getRiasecInsights = (riasecData) => {
    if (!riasecData) return { top: [], bottom: [] };
    
    const entries = Object.entries(riasecData).sort(([,a], [,b]) => b - a);
    return {
      top: entries.slice(0, 3),
      bottom: entries.slice(-3)
    };
  };

  const getRiasecDescription = (type) => {
    const descriptions = {
      'realistic': 'Prefer hands-on, practical work with tools, machines, or animals. Value concrete results and physical activities.',
      'investigative': 'Enjoy analyzing, researching, and solving complex problems. Prefer intellectual challenges and scientific thinking.',
      'artistic': 'Value creativity, self-expression, and aesthetic experiences. Prefer unstructured environments and original work.',
      'social': 'Enjoy helping, teaching, and working with people. Value cooperation, understanding, and making a positive impact.',
      'enterprising': 'Like leading, persuading, and managing others. Value achievement, competition, and business success.',
      'conventional': 'Prefer organized, structured work with clear procedures. Value accuracy, efficiency, and systematic approaches.'
    };
    return descriptions[type] || 'A valuable career interest area';
  };

  const getRiasecCareers = (type) => {
    const careers = {
      'realistic': ['Engineer', 'Mechanic', 'Carpenter', 'Farmer', 'Pilot', 'Chef'],
      'investigative': ['Scientist', 'Researcher', 'Doctor', 'Analyst', 'Psychologist', 'Programmer'],
      'artistic': ['Designer', 'Writer', 'Musician', 'Actor', 'Photographer', 'Architect'],
      'social': ['Teacher', 'Counselor', 'Social Worker', 'Nurse', 'Therapist', 'Coach'],
      'enterprising': ['Manager', 'Sales Representative', 'Entrepreneur', 'Lawyer', 'Marketing Director', 'CEO'],
      'conventional': ['Accountant', 'Administrator', 'Banker', 'Secretary', 'Data Analyst', 'Librarian']
    };
    return careers[type] || [];
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-green-50/30">
      {/* Main Content Area */}
      <main className="flex-1 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Loading State */}
        {!result && !error && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            <EnhancedLoadingScreen
              title="Loading RIASEC Results..."
              subtitle="Fetching your career interests analysis"
              skeletonCount={4}
              className="min-h-[600px]"
            />
          </motion.div>
        )}

        {/* Error State */}
        {error && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.4 }}
            className="bg-red-50 border border-red-200 rounded-lg p-6 shadow-sm"
          >
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Unable to Load Results</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error}</p>
                </div>
                <div className="mt-4 space-x-3">
                  <button
                    onClick={() => window.location.reload()}
                    className="bg-red-100 text-red-800 px-3 py-2 rounded-md text-sm font-medium hover:bg-red-200 transition-colors"
                  >
                    Retry
                  </button>
                  <button
                    onClick={() => navigate(`/results/${resultId}`)}
                    className="bg-gray-100 text-gray-800 px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-200 transition-colors"
                  >
                    Back to Overview
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Content State */}
        {result && (
          <>
            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="mb-8"
            >
              <div className="flex justify-between items-center">
                <h1 className="text-4xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                  RIASEC Career Interests
                </h1>
                <div className="flex space-x-3">
                  <button
                    onClick={() => navigate(`/results/${resultId}`)}
                    className="bg-gradient-to-r from-gray-600 to-gray-700 text-white px-6 py-3 rounded-lg hover:from-gray-700 hover:to-gray-800 transition-colors duration-200 shadow-lg"
                  >
                    Back to Overview
                  </button>
                  <button
                    onClick={() => navigate('/dashboard')}
                    className="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-6 py-3 rounded-lg hover:from-green-700 hover:to-emerald-700 transition-colors duration-200 shadow-lg"
                  >
                    Dashboard
                  </button>
                </div>
              </div>

              <div className="mt-6 p-4 bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="flex items-center space-x-6 text-sm text-gray-600">
                  <div className="flex items-center">
                    <svg className="w-4 h-4 mr-2 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                    </svg>
                    <span>Completed: {formatDate(result.created_at)}</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-green-600 font-medium">🎯 Career Interests Assessment</span>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* RIASEC Chart */}
            {result.assessment_data?.riasec && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className="mb-12"
              >
                <RiasecRadarChart data={result.assessment_data.riasec} />
              </motion.div>
            )}

            {/* Career Interests Analysis */}
            {result.assessment_data?.riasec && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="mb-12"
              >
                <h2 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-8">
                  Your Career Interests Analysis
                </h2>
                
                <div className="grid gap-6">
                  {/* Top Interests */}
                  <div className="bg-gradient-to-br from-green-50 to-emerald-50 p-6 rounded-xl shadow-lg border border-green-100">
                    <h3 className="text-xl font-semibold text-green-700 mb-6 flex items-center">
                      <svg className="w-6 h-6 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      Your Strongest Career Interests
                    </h3>
                    <div className="grid md:grid-cols-3 gap-4">
                      {getRiasecInsights(result.assessment_data.riasec).top.map(([type, score], idx) => (
                        <div key={type} className="bg-white p-4 rounded-lg shadow-sm">
                          <div className="flex justify-between items-center mb-2">
                            <h4 className="font-semibold text-gray-900 capitalize text-lg">
                              {type}
                            </h4>
                            <span className="text-green-600 font-bold text-xl">{score.toFixed(1)}</span>
                          </div>
                          <p className="text-sm text-gray-600 mb-3">{getRiasecDescription(type)}</p>
                          <div className="mb-3 bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-green-500 h-2 rounded-full transition-all duration-500"
                              style={{ width: `${(score / 5) * 100}%` }}
                            ></div>
                          </div>
                          <div>
                            <h5 className="text-xs font-medium text-gray-700 mb-2">Related Careers:</h5>
                            <div className="flex flex-wrap gap-1">
                              {getRiasecCareers(type).slice(0, 4).map((career, careerIdx) => (
                                <span key={careerIdx} className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                                  {career}
                                </span>
                              ))}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Lower Interests */}
                  <div className="bg-gradient-to-br from-orange-50 to-amber-50 p-6 rounded-xl shadow-lg border border-orange-100">
                    <h3 className="text-xl font-semibold text-orange-700 mb-6 flex items-center">
                      <svg className="w-6 h-6 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      Areas to Explore or Develop
                    </h3>
                    <div className="grid md:grid-cols-3 gap-4">
                      {getRiasecInsights(result.assessment_data.riasec).bottom.map(([type, score], idx) => (
                        <div key={type} className="bg-white p-4 rounded-lg shadow-sm">
                          <div className="flex justify-between items-center mb-2">
                            <h4 className="font-semibold text-gray-900 capitalize text-lg">
                              {type}
                            </h4>
                            <span className="text-orange-600 font-bold text-xl">{score.toFixed(1)}</span>
                          </div>
                          <p className="text-sm text-gray-600 mb-3">{getRiasecDescription(type)}</p>
                          <div className="mb-3 bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-orange-500 h-2 rounded-full transition-all duration-500"
                              style={{ width: `${(score / 5) * 100}%` }}
                            ></div>
                          </div>
                          <div>
                            <h5 className="text-xs font-medium text-gray-700 mb-2">Related Careers:</h5>
                            <div className="flex flex-wrap gap-1">
                              {getRiasecCareers(type).slice(0, 4).map((career, careerIdx) => (
                                <span key={careerIdx} className="text-xs bg-orange-100 text-orange-700 px-2 py-1 rounded">
                                  {career}
                                </span>
                              ))}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Assessment Explanations */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <AssessmentExplanations showOnly="riasec" />
            </motion.div>
          </>
        )}
      </main>
    </div>
  );
};

export default ResultRiasec;
