import { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import apiService from '../../services/apiService';
import EnhancedLoadingScreen from '../UI/EnhancedLoadingScreen';
import OceanBarChart from './OceanBarChart';
import AssessmentExplanations from './AssessmentExplanations';

const ResultOcean = () => {
  const { resultId } = useParams();
  const navigate = useNavigate();
  const [result, setResult] = useState(null);
  const [error, setError] = useState('');
  const fetchInProgressRef = useRef(false);
  const abortControllerRef = useRef(null);

  useEffect(() => {
    // Prevent duplicate calls
    if (fetchInProgressRef.current) {
      return;
    }

    const fetchResult = async (retryCount = 0) => {
      const maxRetries = 5;
      const retryDelay = Math.min(1000 * Math.pow(2, retryCount), 10000); // Exponential backoff, max 10s

      // Create new AbortController for this fetch sequence
      abortControllerRef.current = new AbortController();

      try {
        fetchInProgressRef.current = true;
        const response = await apiService.getResultById(resultId);

        // Check if component is still mounted and request wasn't aborted
        if (!abortControllerRef.current?.signal.aborted) {
          if (response.success) {
            setResult(response.data);
            fetchInProgressRef.current = false;
          }
        }
      } catch (err) {
        // Check if the error is due to abort
        if (abortControllerRef.current?.signal.aborted) {
          return;
        }

        // If it's a 404 and we haven't exceeded max retries, try again
        if (err.response?.status === 404 && retryCount < maxRetries) {
          setTimeout(() => {
            // Check if component is still mounted before retrying
            if (!abortControllerRef.current?.signal.aborted) {
              fetchResult(retryCount + 1);
            }
          }, retryDelay);
        } else {
          // Final error after all retries or non-404 error
          const errorMessage = retryCount >= maxRetries
            ? `Result not found after ${maxRetries + 1} attempts. The analysis may still be processing.`
            : err.response?.data?.message || 'Failed to load results';
          setError(errorMessage);
          fetchInProgressRef.current = false;
        }
      }
    };

    if (resultId) {
      fetchResult();
    } else {
      navigate('/dashboard');
    }

    // Cleanup function
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      fetchInProgressRef.current = false;
    };
  }, [resultId, navigate]);

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getOceanInsights = (oceanData) => {
    if (!oceanData) return { high: [], low: [] };
    
    const entries = Object.entries(oceanData).sort(([,a], [,b]) => b - a);
    return {
      high: entries.filter(([,score]) => score >= 3.5),
      low: entries.filter(([,score]) => score < 2.5)
    };
  };

  const getTraitDescription = (trait) => {
    const descriptions = {
      'openness': 'Openness to experience reflects your willingness to try new things, think creatively, and embrace novel ideas and experiences.',
      'conscientiousness': 'Conscientiousness measures your tendency to be organized, responsible, dependable, and goal-oriented in your approach to tasks.',
      'extraversion': 'Extraversion indicates your preference for social interaction, assertiveness, and drawing energy from external stimulation.',
      'agreeableness': 'Agreeableness reflects your tendency to be cooperative, trusting, helpful, and considerate in your interactions with others.',
      'neuroticism': 'Neuroticism measures your emotional stability and tendency to experience negative emotions like anxiety, stress, or mood swings.'
    };
    return descriptions[trait] || 'A personality trait that influences behavior and preferences';
  };

  const getTraitImplications = (trait, score) => {
    const implications = {
      'openness': {
        high: ['Creative problem-solving', 'Adaptable to change', 'Enjoys learning new skills', 'Values artistic experiences'],
        low: ['Prefers routine and structure', 'Practical approach', 'Values tradition', 'Focused on proven methods']
      },
      'conscientiousness': {
        high: ['Strong work ethic', 'Reliable and punctual', 'Goal-oriented', 'Good at planning'],
        low: ['Flexible and spontaneous', 'Comfortable with ambiguity', 'Adaptable to change', 'Less rigid in approach']
      },
      'extraversion': {
        high: ['Energized by social interaction', 'Comfortable in leadership roles', 'Assertive communicator', 'Enjoys team environments'],
        low: ['Prefers quiet environments', 'Thoughtful and reflective', 'Works well independently', 'Careful decision-maker']
      },
      'agreeableness': {
        high: ['Cooperative team player', 'Empathetic and supportive', 'Avoids conflict', 'Values harmony'],
        low: ['Direct and honest communicator', 'Competitive nature', 'Independent thinker', 'Willing to challenge others']
      },
      'neuroticism': {
        high: ['Sensitive to stress', 'Emotionally responsive', 'Motivated by security', 'Benefits from support systems'],
        low: ['Emotionally stable', 'Calm under pressure', 'Resilient to stress', 'Consistent performance']
      }
    };
    
    return score >= 3.5 ? implications[trait]?.high || [] : implications[trait]?.low || [];
  };

  const getScoreLevel = (score) => {
    if (score >= 4.5) return { level: 'Very High', color: 'text-purple-600 bg-purple-100' };
    if (score >= 3.5) return { level: 'High', color: 'text-blue-600 bg-blue-100' };
    if (score >= 2.5) return { level: 'Moderate', color: 'text-yellow-600 bg-yellow-100' };
    if (score >= 1.5) return { level: 'Low', color: 'text-orange-600 bg-orange-100' };
    return { level: 'Very Low', color: 'text-red-600 bg-red-100' };
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-purple-50/30">
      {/* Main Content Area */}
      <main className="flex-1 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Loading State */}
        {!result && !error && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            <EnhancedLoadingScreen
              title="Loading OCEAN Results..."
              subtitle="Fetching your personality analysis"
              skeletonCount={4}
              className="min-h-[600px]"
            />
          </motion.div>
        )}

        {/* Error State */}
        {error && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.4 }}
            className="bg-red-50 border border-red-200 rounded-lg p-6 shadow-sm"
          >
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Unable to Load Results</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error}</p>
                </div>
                <div className="mt-4 space-x-3">
                  <button
                    onClick={() => window.location.reload()}
                    className="bg-red-100 text-red-800 px-3 py-2 rounded-md text-sm font-medium hover:bg-red-200 transition-colors"
                  >
                    Retry
                  </button>
                  <button
                    onClick={() => navigate(`/results/${resultId}`)}
                    className="bg-gray-100 text-gray-800 px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-200 transition-colors"
                  >
                    Back to Overview
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Content State */}
        {result && (
          <>
            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="mb-8"
            >
              <div className="flex justify-between items-center">
                <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                  OCEAN Personality Traits
                </h1>
                <div className="flex space-x-3">
                  <button
                    onClick={() => navigate(`/results/${resultId}`)}
                    className="bg-gradient-to-r from-gray-600 to-gray-700 text-white px-6 py-3 rounded-lg hover:from-gray-700 hover:to-gray-800 transition-colors duration-200 shadow-lg"
                  >
                    Back to Overview
                  </button>
                  <button
                    onClick={() => navigate('/dashboard')}
                    className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-3 rounded-lg hover:from-purple-700 hover:to-pink-700 transition-colors duration-200 shadow-lg"
                  >
                    Dashboard
                  </button>
                </div>
              </div>

              <div className="mt-6 p-4 bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="flex items-center space-x-6 text-sm text-gray-600">
                  <div className="flex items-center">
                    <svg className="w-4 h-4 mr-2 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                    </svg>
                    <span>Completed: {formatDate(result.created_at)}</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-purple-600 font-medium">🧠 Personality Assessment</span>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* OCEAN Chart */}
            {result.assessment_data?.ocean && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className="mb-12"
              >
                <OceanBarChart data={result.assessment_data.ocean} />
              </motion.div>
            )}

            {/* Personality Analysis */}
            {result.assessment_data?.ocean && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="mb-12"
              >
                <h2 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-8">
                  Your Personality Analysis
                </h2>
                
                <div className="grid gap-6">
                  {/* All Traits Detailed View */}
                  <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
                    <h3 className="text-xl font-semibold text-gray-800 mb-6 flex items-center">
                      <svg className="w-6 h-6 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                      </svg>
                      Detailed Personality Profile
                    </h3>
                    <div className="grid gap-6">
                      {Object.entries(result.assessment_data.ocean).map(([trait, score]) => {
                        const scoreInfo = getScoreLevel(score);
                        const implications = getTraitImplications(trait, score);
                        
                        return (
                          <div key={trait} className="bg-gray-50 p-5 rounded-lg">
                            <div className="flex justify-between items-center mb-3">
                              <h4 className="font-semibold text-gray-900 capitalize text-lg">
                                {trait}
                              </h4>
                              <div className="flex items-center space-x-3">
                                <span className={`px-3 py-1 rounded-full text-sm font-medium ${scoreInfo.color}`}>
                                  {scoreInfo.level}
                                </span>
                                <span className="text-gray-800 font-bold text-xl">{score.toFixed(1)}</span>
                              </div>
                            </div>
                            
                            <p className="text-sm text-gray-600 mb-4">{getTraitDescription(trait)}</p>
                            
                            <div className="mb-4 bg-gray-200 rounded-full h-3">
                              <div 
                                className="bg-gradient-to-r from-purple-500 to-pink-500 h-3 rounded-full transition-all duration-500"
                                style={{ width: `${(score / 5) * 100}%` }}
                              ></div>
                            </div>
                            
                            {implications.length > 0 && (
                              <div>
                                <h5 className="text-sm font-medium text-gray-700 mb-2">
                                  What this means for you:
                                </h5>
                                <div className="grid md:grid-cols-2 gap-2">
                                  {implications.map((implication, idx) => (
                                    <div key={idx} className="text-sm text-gray-600 flex items-start">
                                      <span className="text-purple-500 mr-2 mt-1">•</span>
                                      {implication}
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  {/* Summary Insights */}
                  <div className="grid md:grid-cols-2 gap-6">
                    {/* Strengths */}
                    {getOceanInsights(result.assessment_data.ocean).high.length > 0 && (
                      <div className="bg-gradient-to-br from-green-50 to-emerald-50 p-6 rounded-xl shadow-lg border border-green-100">
                        <h3 className="text-xl font-semibold text-green-700 mb-4 flex items-center">
                          <svg className="w-6 h-6 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                          Prominent Traits
                        </h3>
                        <div className="space-y-3">
                          {getOceanInsights(result.assessment_data.ocean).high.map(([trait, score]) => (
                            <div key={trait} className="bg-white p-3 rounded-lg shadow-sm">
                              <div className="flex justify-between items-center">
                                <span className="font-medium text-gray-900 capitalize">{trait}</span>
                                <span className="text-green-600 font-bold">{score.toFixed(1)}</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Areas for Balance */}
                    {getOceanInsights(result.assessment_data.ocean).low.length > 0 && (
                      <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-xl shadow-lg border border-blue-100">
                        <h3 className="text-xl font-semibold text-blue-700 mb-4 flex items-center">
                          <svg className="w-6 h-6 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                          </svg>
                          Areas for Balance
                        </h3>
                        <div className="space-y-3">
                          {getOceanInsights(result.assessment_data.ocean).low.map(([trait, score]) => (
                            <div key={trait} className="bg-white p-3 rounded-lg shadow-sm">
                              <div className="flex justify-between items-center">
                                <span className="font-medium text-gray-900 capitalize">{trait}</span>
                                <span className="text-blue-600 font-bold">{score.toFixed(1)}</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </motion.div>
            )}

            {/* Assessment Explanations */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <AssessmentExplanations showOnly="ocean" />
            </motion.div>
          </>
        )}
      </main>
    </div>
  );
};

export default ResultOcean;
